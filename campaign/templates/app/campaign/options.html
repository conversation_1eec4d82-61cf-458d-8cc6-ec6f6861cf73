{% extends "app/campaign/campaign-base.html" %}

{% block title %}Campaign options{% endblock %}

{% block cmp-base-content %}

<style>
/* Custom styles for options page */
.option-card {
    transition: all 0.2s ease;
}

.option-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.toggle-group .btn {
    transition: all 0.2s ease;
}

.toggle-group .btn.active {
    transform: scale(1.02);
}

.form-control input:focus,
.form-control select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.save-indicator {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.save-indicator.show {
    opacity: 1;
}

/* Add this to your styles */
#dropdownList {
    max-width: calc(100% - 2px); /* Adjust as needed */
}
</style>

        <!-- Options Container -->
        <div class="mt-6 w-full max-w-4xl mx-auto">
            <form id="options-form" class="space-y-4">
                <!-- Card: Accounts to use -->
                <div class="card bg-white p-6 rounded-lg shadow-sm flex-row items-center justify-between option-card">
                    <div>
                        <h3 class="font-bold text-gray-800 text-base">Accounts to use</h3>
                        <p class="text-sm text-gray-500 mt-1">Select one or more accounts to send emails from</p>
                    </div>
                    <div class="flex flex-col items-end gap-2">
                        <div class="max-w-lg w-full mx-auto relative">
                            <label class="block text-sm font-medium mb-1">Select Sending Emails</label>

                            <!-- Selected Email Tags -->
                            <div id="selectedEmails" class="flex flex-wrap gap-2 mb-2">
                                {% if campaign_options %}
                                    {% for email in campaign_options.email_accounts.all %}
                                        <div class="badge badge-success badge-outline text-xs font-semibold">
                                            {{ email.email }}
                                            <button class="ml-2 text-error" onclick="removeEmail('{{ email.email }}')">×</button>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            </div>

                            <!-- Search Input -->
                            <input type="text" id="searchInput" placeholder="Search email..."
                                class="input input-bordered w-full mb-2" oninput="filterEmails()" onclick="showDropdown()" />

                            <!-- Dropdown List - Updated classes -->
                            <ul id="dropdownList" class="menu bg-white border rounded-box max-h-70 hidden shadow-md absolute top-full left-0 right-0 z-10 mt-1 w-full" 
                                style="overflow-y: auto;overflow-x: none;max-width: calc(100% - 2px);"></ul>
                        </div>

                        <button type="button" class="text-sm text-primary font-semibold hover:underline" onclick="connectNewAccount()">
                            Connect new email account
                        </button>
                    </div>
                </div>
                <!-- Card: Stop sending emails -->
                <div class="card bg-white p-6 rounded-lg shadow-sm flex-row items-center justify-between option-card">
                    <div>
                        <h3 class="font-bold text-gray-800 text-base">Stop sending emails on reply</h3>
                        <p class="text-sm text-gray-500 mt-1">Stop sending emails to a lead if a response has been received</p>
                    </div>
                    <div class="join rounded-lg toggle-group" data-option="stopOnReply">
                        <button type="button" class="join-item btn btn-sm {% if not campaign_options or not campaign_options.stop_on_reply %}bg-gray-100 border-gray-300 text-gray-500{% else %}btn-neutral text-white{% endif %}"
                                onclick="toggleOption('stopOnReply', false)" data-value="false">Disable</button>
                        <button type="button" class="join-item btn btn-sm {% if campaign_options and campaign_options.stop_on_reply %}btn-success text-white active{% else %}bg-gray-100 border-gray-300 text-gray-500{% endif %}"
                                onclick="toggleOption('stopOnReply', true)" data-value="true">Enable</button>
                    </div>
                </div>

                <!-- Card: Open Tracking -->
                <div class="card bg-white p-6 rounded-lg shadow-sm flex-row items-center justify-between option-card">
                    <div>
                        <h3 class="font-bold text-gray-800 text-base">Open Tracking</h3>
                        <p class="text-sm text-gray-500 mt-1">Track email opens</p>
                    </div>
                    <div class="flex items-center gap-4">
                        <label class="label cursor-pointer gap-2">
                            <input type="checkbox" name="linkTracking" class="checkbox checkbox-primary" onchange="updateOption('linkTracking', this.checked)" {% if campaign_options and campaign_options.link_tracking_enabled %}checked{% endif %}/>
                            <span class="label-text">Link tracking</span>
                        </label>
                        <div class="join rounded-lg toggle-group" data-option="openTracking">
                            <button type="button" class="join-item btn btn-sm {% if not campaign_options or not campaign_options.open_tracking_enabled %}bg-gray-100 border-gray-300 text-gray-500{% else %}btn-neutral text-white{% endif %}" 
                                    onclick="toggleOption('openTracking', false)" data-value="false">Disable</button>
                            <button type="button" class="join-item btn btn-sm {% if campaign_options and campaign_options.open_tracking_enabled %}btn-success text-white active{% else %}bg-gray-100 border-gray-300 text-gray-500{% endif %}" 
                                    onclick="toggleOption('openTracking', true)" data-value="true">Enable</button>
                        </div>
                    </div>
                </div>
                <!-- Card: Delivery Optimization -->
                <div class="card bg-white p-6 rounded-lg shadow-sm flex-row items-center justify-between option-card">
                    <div>
                        <h3 class="font-bold text-gray-800 text-base flex items-center gap-2">
                            Delivery Optimization
                            <div class="badge badge-success badge-outline text-xs font-semibold">Recommended</div>
                        </h3>
                        <p class="text-sm text-gray-500 mt-1">Disables open tracking</p>
                    </div>
                    <div class="flex flex-col items-start gap-2">
                        <label class="label cursor-pointer justify-start gap-2">
                            <input type="checkbox" name="textOnly" class="checkbox checkbox-primary" onchange="updateOption('textOnly', this.checked)" {% if campaign_options and campaign_options.send_as_text_only %}checked{% endif %}/>
                            <span class="label-text text-sm">Send emails as text-only (no HTML)</span>
                        </label>
                        <label class="label cursor-pointer justify-start gap-2">
                            <input type="checkbox" name="firstEmailTextOnly" class="checkbox checkbox-primary" onchange="updateOption('firstEmailTextOnly', this.checked)" {% if not campaign_options or campaign_options.send_first_email_as_text_only %}checked{% endif %}/>
                            <span class="label-text text-sm flex items-center gap-2">
                                Send first email as text-only
                                <!-- <div class="badge badge-warning text-yellow-800 font-bold text-xs">Pro</div> -->
                            </span>
                        </label>
                    </div>
                </div>

                <!-- Card: Daily Limit -->
                <div class="card bg-white p-6 rounded-lg shadow-sm flex-row items-center justify-between option-card">
                    <div>
                        <h3 class="font-bold text-gray-800 text-base">Daily Limit</h3>
                        <p class="text-sm text-gray-500 mt-1">Max number of emails to send per day for this campaign.</p>
                        <p class="text-sm text-gray-500 mt-1">Each email can send 30-40 emails per day, without landing in spam/promotion folders</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <input type="number"
                               id="daily-limit"
                               name="dailyLimit"
                               value="{% if campaign_options %}{{ campaign_options.daily_limit }}{% else %}30{% endif %}"
                               min="1"
                               max="1000"
                               class="input input-bordered bg-white w-24 text-center"
                               onchange="updateOption('dailyLimit', this.value)" />
                        <span class="text-sm text-gray-500 save-indicator" id="daily-limit-saved">
                            <i class="fa-solid fa-check text-green-500"></i>
                        </span>
                    </div>
                </div>
            </form>

            <!-- Action Buttons -->
            <div class="flex justify-center mt-10 space-x-4">
                <button class="btn btn-outline btn-primary" onclick="saveOptions()">
                    <i class="fas fa-save mr-2"></i> Save
                </button>
                <button class="btn btn-primary" onclick="launchCampaign()">
                    <i class="fas fa-rocket mr-2"></i> Launch
                </button>
            </div>
        </div>

<script>
    const allEmails = [
    {% for email in emails %}
        { email_id: {{email.email_id}}, email: "{{email.email}}", active: {% if email.active %}true{% else %}false{% endif %}, used: {% if email.used %}true{% else %}false{% endif %} },
    {% endfor %}
    ];

    // Initialize selected emails with current campaign options
    let selectedEmails = [
        {% if campaign_options %}
            {% for email in campaign_options.email_accounts.all %}
                { email_id: {{email.id}}, email: "{{email.email}}"},
            {% endfor %}
        {% endif %}
    ];

    const dropdown = document.getElementById("dropdownList");
    const searchInput = document.getElementById("searchInput");
    const selectedContainer = document.getElementById("selectedEmails");

    // Initialize campaign options with current values
    let campaignOptions = {
        emailAccounts: selectedEmails,
        stopOnReply: {% if campaign_options %}{{ campaign_options.stop_on_reply|lower }}{% else %}true{% endif %},
        openTracking: {% if campaign_options %}{{ campaign_options.open_tracking_enabled|lower }}{% else %}true{% endif %},
        linkTracking: {% if campaign_options %}{{ campaign_options.link_tracking_enabled|lower }}{% else %}false{% endif %},
        textOnly: {% if campaign_options %}{{ campaign_options.send_as_text_only|lower }}{% else %}false{% endif %},
        firstEmailTextOnly: {% if not campaign_options or campaign_options.send_first_email_as_text_only %}true{% else %}false{% endif %},
        dailyLimit: {% if campaign_options %}{{ campaign_options.daily_limit }}{% else %}30{% endif %}
    };

    function showDropdown() {
      dropdown.classList.remove("hidden");
      filterEmails();
    }

    document.addEventListener("click", function (e) {
      if (!dropdown.contains(e.target) && e.target !== searchInput) {
        dropdown.classList.add("hidden");
      }
    });

    function filterEmails() {
      const query = searchInput.value.toLowerCase();
      dropdown.innerHTML = "";

      const filtered = allEmails.filter(e =>
        e.email.toLowerCase().includes(query) &&
        !selectedEmails.includes(e.email)
      );

      if (filtered.length === 0) {
        dropdown.innerHTML = `<li clascampaign_optionss="text-sm text-gray-500 px-4 py-2">No options</li>`;
        return;
      }

      filtered.forEach(email => {
        const li = document.createElement("li");
        li.className = "px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm flex justify-between items-center";
        li.innerHTML = `
          <span>${email.email}</span>
          ${!email.active ? '<span class="text-red-500 text-xs ml-2">Account not active</span>' : ''}
          ${email.used ? '<span class="text-red-400 text-xs ml-2">Account already used</span>' : ''}
        `;

        if (email.active && !email.used) {
          li.onclick = () => selectEmail(email.email);
        } else {
          li.classList.add("opacity-50", "cursor-not-allowed");
        }

        dropdown.appendChild(li);
      });
    }

    function selectEmail(email) {
      if (!selectedEmails.includes(email)) {
        selectedEmails.push(email);
        campaignOptions.emailAccounts = selectedEmails;
        renderSelected();
        searchInput.value = "";
        filterEmails();
      }
    }

    function removeEmail(email) {
      selectedEmails = selectedEmails.filter(e => e.email_id !== email.email_id);
      campaignOptions.emailAccounts = selectedEmails;
      renderSelected();
      filterEmails();
    }

    function renderSelected() {
      selectedContainer.innerHTML = "";
      selectedEmails.forEach(email => {
        const badge = document.createElement("div");
        badge.className = "badge badge-success badge-outline text-xs font-semibold";
        badge.innerHTML = `
          ${email}
          <button class="ml-2 text-error" onclick="removeEmail('${email}')">×</button>
        `;
        selectedContainer.appendChild(badge);
      });
    }

    // Toggle button functionality
    function toggleOption(optionName, value) {
        const toggleGroup = document.querySelector(`[data-option="${optionName}"]`);
        const buttons = toggleGroup.querySelectorAll('button');

        // Update button states
        buttons.forEach(btn => {
            const btnValue = btn.getAttribute('data-value') === 'true';
            if (btnValue === value) {
                btn.classList.add('active');
                if (value) {
                    btn.classList.add('btn-success', 'text-white');
                    btn.classList.remove('bg-gray-100', 'border-gray-300', 'text-gray-500', 'btn-neutral');
                } else {
                    btn.classList.add('btn-neutral', 'text-white');
                    btn.classList.remove('bg-gray-100', 'border-gray-300', 'text-gray-500', 'btn-success');
                }
            } else {
                btn.classList.remove('active', 'btn-success', 'btn-neutral', 'text-white');
                btn.classList.add('bg-gray-100', 'border-gray-300', 'text-gray-500');
            }
        });

        // Update option value
        campaignOptions[optionName] = value;
    }

    // Update checkbox options
    function updateOption(optionName, value) {
        campaignOptions[optionName] = value;
        
        // Show save indicator for daily limit changes
        if (optionName === 'dailyLimit') {
            const indicator = document.getElementById('daily-limit-saved');
            indicator.classList.add('show');
            setTimeout(() => indicator.classList.remove('show'), 2000);
        }
    }

    function saveOptions() {
        console.log('Saving options:', campaignOptions);

        campaignOptionsToSave = {
            campaign: {{ campaign_id }},
            email_accounts: campaignOptions.emailAccounts, //this should be an array of email account ids not email accounts emails
            stop_on_reply: campaignOptions.stopOnReply, //this should a biolen but as python (like TRUE/FALSE not true/false like js) 
            open_tracking_enabled: campaignOptions.openTracking,  //this should a biolen but as python (like TRUE/FALSE not true/false like js)
            link_tracking_enabled: campaignOptions.linkTracking, //this should a biolen but as python (like TRUE/FALSE not true/false like js)
            send_as_text_only: campaignOptions.textOnly, //this should a biolen but as python (like TRUE/FALSE not true/false like js)
            send_first_email_as_text_only: campaignOptions.firstEmailTextOnly, //this should a biolen but as python (like TRUE/FALSE not true/false like js)
            daily_limit: campaignOptions.dailyLimit // this is integer
        }
        
        fetch('/api/campaign-options/{% if campaign_options %}{{ campaign_options.id }}{% else %}0{% endif %}/', {
            method: {% if campaign_options %}'PUT'{% else %}'POST'{% endif %},
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify(campaignOptionsToSave)
        })
        .then(response => response.json())
        .then(data => {
            ModalSystem.toast('Campaign options saved successfully!');
        })
        .catch(error => {
            ModalSystem.error('Failed to save options');
        });
    }

    // Launch campaign - TODO: Implement actual launch functionality
    function launchCampaign() {
        // Validate required fields
        if (campaignOptions.emailAccounts.length === 0) {
            ModalSystem.error('Please select at least one email account before launching the campaign.');
            return;
        }

        ModalSystem.confirm({
            title: 'Launch Campaign',
            message: 'Are you sure you want to launch this campaign? ',
            confirmText: 'Launch',
            confirmClass: 'btn-primary',
            action: () => {
                ModalSystem.loading('Launching campaign...');

                setTimeout(() => {
                    ModalSystem.hideLoading();
                    ModalSystem.success({
                        title: 'Campaign Launched!',
                        message: 'Your campaign has been successfully launched and is now active.',
                        action: () => {
                            // Redirect to dashboard
                            window.location.href = '/campaign/dashboard/{{campaign_id}}';
                        }
                    });
                }, 2000);
                
                // TODO: Implement actual launch API call
                /*
                fetch('/api/campaign/{{ campaign_id }}/launch/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: JSON.stringify(campaignOptions)
                })
                .then(response => response.json())
                .then(data => {
                    window.ModalSystem.hideLoading();
                    if (data.success) {
                        window.ModalSystem.success({
                            title: 'Campaign Launched!',
                            message: 'Your campaign has been successfully launched and is now active.',
                            action: () => {
                                window.location.href = '/campaign/dashboard/{{campaign_id}}';
                            }
                        });
                    } else {
                        window.ModalSystem.error(data.message || 'Failed to launch campaign');
                    }
                })
                .catch(error => {
                    window.ModalSystem.hideLoading();
                    window.ModalSystem.error('Failed to launch campaign');
                });
                */
            }
        });
    }

    // Initialize the UI with current values
    document.addEventListener('DOMContentLoaded', function() {
        // Set initial toggle states
        toggleOption('stopOnReply', campaignOptions.stopOnReply);
        toggleOption('openTracking', campaignOptions.openTracking);
        
        // Render selected emails
        renderSelected();
    });
</script>

{% endblock %}